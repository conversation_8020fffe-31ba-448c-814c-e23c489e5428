# User Approval System - Testing Guide

## Overview
This guide outlines how to test the manual user approval system that has been implemented.

## System Components

### 1. Database Changes
- Added `approval_status` field to profiles table
- Created database trigger to auto-create profiles with 'pending' status
- Added ApprovalStatus enum: 'pending', 'approved', 'rejected'

### 2. Authentication Flow
- Modified AuthContext to check approval status
- Users with 'pending' status are redirected to pending approval page
- Users with 'rejected' status are signed out
- Only 'approved' users can access the dashboard

### 3. Admin Interface
- New `/user-approvals` page for admins to manage approvals
- API endpoints for fetching and updating user approval status
- Statistics dashboard showing approval metrics

### 4. User Experience
- Pending approval page with clear messaging
- Login page with approval process information
- Automatic redirects based on approval status

## Testing Steps

### Step 1: Database Setup
1. Run the SQL migration to add approval_status column:
   ```sql
   -- Execute the contents of supabase/migrations/001_create_profile_trigger.sql
   ```

### Step 2: Test New User Registration
1. Go to `/login` page
2. Register a new user (using Google/Apple auth)
3. Verify:
   - User is redirected to `/pending-approval` page
   - Profile is created with `approval_status = 'pending'`
   - User cannot access `/dashboard` or other protected routes

### Step 3: Test Pending User Experience
1. As a pending user, verify:
   - Pending approval page displays correctly
   - "Check Approval Status" button works
   - Sign out functionality works
   - Cannot access protected routes (middleware redirects to pending page)

### Step 4: Test Admin Approval Interface
1. Manually set an existing user's approval_status to 'approved' in database
2. Login as admin user
3. Navigate to `/user-approvals`
4. Verify:
   - Can see pending users list
   - Statistics cards show correct counts
   - Can approve/reject users
   - Tabs filter users correctly

### Step 5: Test Approval Process
1. As admin, approve a pending user
2. Verify:
   - User's approval_status changes to 'approved' in database
   - User can now access dashboard when they login
   - User is redirected from pending page to dashboard

### Step 6: Test Rejection Process
1. As admin, reject a pending user
2. Verify:
   - User's approval_status changes to 'rejected' in database
   - User is signed out and redirected to login when they try to access the app
   - User cannot access any protected routes

### Step 7: Test Middleware Protection
1. Try accessing protected routes with different user states:
   - No authentication: redirected to `/login`
   - Pending approval: redirected to `/pending-approval`
   - Rejected: signed out and redirected to `/login`
   - Approved: can access routes based on role

### Step 8: Test API Endpoints
Test the following endpoints:
- `GET /api/admin/user-approvals` - Fetch users
- `GET /api/admin/user-approvals?status=pending` - Fetch pending users
- `PUT /api/admin/user-approvals` - Update user approval status
- `GET /api/admin/approval-stats` - Get approval statistics
- `POST /api/auth/create-profile` - Create user profile

## Expected Behavior

### For New Users:
1. Register → Profile created with 'pending' status
2. Redirected to pending approval page
3. Cannot access dashboard or protected routes
4. Can sign out

### For Pending Users:
1. Login → Redirected to pending approval page
2. All protected routes redirect to pending page
3. Can check status and sign out

### For Approved Users:
1. Login → Redirected to dashboard
2. Can access all routes based on role permissions
3. Normal application functionality

### For Rejected Users:
1. Login attempt → Automatically signed out
2. Redirected to login page
3. Cannot access any protected routes

### For Admins:
1. Can access `/user-approvals` page
2. Can view all users and their approval status
3. Can approve/reject users
4. Can see approval statistics

## Security Considerations

1. **Middleware Protection**: All routes are protected by middleware that checks approval status
2. **API Security**: Admin endpoints should verify admin role (implement proper auth checks)
3. **Database Triggers**: Automatic profile creation ensures no user bypasses approval process
4. **Role-based Access**: Only admin/master roles can access approval management

## Troubleshooting

### Common Issues:
1. **Profile not created**: Check database trigger is working
2. **Infinite redirects**: Verify middleware logic and route configurations
3. **API errors**: Check Supabase permissions and RLS policies
4. **UI not updating**: Verify state management in React components

### Debug Steps:
1. Check browser console for errors
2. Verify database records for approval_status
3. Test API endpoints directly
4. Check middleware logs
5. Verify Supabase Auth configuration

## Next Steps

After testing, consider:
1. Adding email notifications for approval status changes
2. Implementing audit logs for approval actions
3. Adding bulk approval functionality
4. Creating approval workflow with multiple stages
5. Adding user profile completion requirements
