"use client";

import { useAuth } from "@/context/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Clock, RefreshCw, Info, Mail, CheckCircle } from "lucide-react";

const PendingApprovalPage = () => {
  const { user, loading, signOut, isPending, isApproved } = useAuth();
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showLoginMessage, setShowLoginMessage] = useState(false);

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push("/login");
      } else if (isApproved) {
        router.push("/dashboard");
      }
    }
  }, [user, loading, isApproved, isPending, router]);

  // Show login message if user just logged in with pending status
  useEffect(() => {
    if (user && isPending) {
      setShowLoginMessage(true);
      // Auto-hide after 10 seconds
      const timer = setTimeout(() => {
        setShowLoginMessage(false);
      }, 10000);
      return () => clearTimeout(timer);
    }
  }, [user, isPending]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Force a page reload to check the latest approval status
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleSignOut = async () => {
    await signOut();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user || !isPending) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
            <Clock className="w-8 h-8 text-yellow-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Account Pending Approval
          </CardTitle>
          <CardDescription className="text-gray-600">
            Your account is currently under review by our administrators
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Show message when user just logged in with pending status */}
          {showLoginMessage && (
            <Alert className="bg-blue-50 border-blue-200">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <div className="space-y-1">
                  <div><strong>Login Successful</strong></div>
                  <p className="text-sm">
                    You have successfully logged in, but your account is currently pending approval.
                    This page shows your current status and what to expect next.
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Clock className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-yellow-800">What happens next?</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  Our team will review your account and approve it within 24-48 hours.
                  You'll receive an email notification once your account is approved.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Mail className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-800">Account Details</h3>
                <p className="text-sm text-blue-700 mt-1">
                  <strong>Email:</strong> {user.email}
                </p>
                <p className="text-sm text-blue-700">
                  <strong>Status:</strong> Pending Approval
                </p>
                <p className="text-sm text-blue-700">
                  <strong>Role:</strong> {user.role}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-green-800">Once Approved</h3>
                <p className="text-sm text-green-700 mt-1">
                  You'll have full access to the Paiper admin dashboard and all its features.
                  You can manage orders, inventory, users, and more.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="w-full"
              variant="outline"
            >
              {isRefreshing ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Checking Status...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Check Approval Status
                </>
              )}
            </Button>

            <Button
              onClick={handleSignOut}
              variant="ghost"
              className="w-full"
            >
              Sign Out
            </Button>
          </div>

          <div className="text-center text-sm text-gray-500">
            <p>Need help? Contact us at:</p>
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              <EMAIL>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PendingApprovalPage;