import { NextRequest, NextResponse } from 'next/server';
import { supabase as supabaseServerClient } from '@/lib/supabaseClient';

// GET - Fetch users for approval management
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = req.nextUrl;
    const status = searchParams.get('status'); // 'pending', 'approved', 'rejected', or null for all
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    let query = supabaseServerClient
      .from('profiles')
      .select('id, email, name, role, approval_status, created_at')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filter by status if provided
    if (status && ['pending', 'approved', 'rejected'].includes(status)) {
      query = query.eq('approval_status', status);
    }

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      data,
      count: data?.length || 0
    }, { status: 200 });

  } catch (error: any) {
    console.error('Error fetching users for approval:', error.message);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// PUT - Update user approval status
export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, approval_status, adminId } = body;

    // Validate required fields
    if (!userId || !approval_status) {
      return NextResponse.json({
        success: false,
        error: 'User ID and approval status are required'
      }, { status: 400 });
    }

    // Validate approval status
    if (!['pending', 'approved', 'rejected'].includes(approval_status)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid approval status. Must be pending, approved, or rejected'
      }, { status: 400 });
    }

    // TODO: Add admin authentication check here
    // For now, we'll assume the request is from an authenticated admin

    // Update the user's approval status
    const { data, error } = await supabaseServerClient
      .from('profiles')
      .update({
        approval_status,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select('id, email, name, role, approval_status, updated_at')
      .single();

    if (error) {
      throw error;
    }

    // Log the approval action (optional - could be useful for audit trail)
    console.log(`User ${userId} approval status changed to ${approval_status} by admin ${adminId || 'unknown'}`);

    return NextResponse.json({
      success: true,
      data,
      message: `User approval status updated to ${approval_status}`
    }, { status: 200 });

  } catch (error: any) {
    console.error('Error updating user approval status:', error.message);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// POST - Batch update multiple users
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userIds, approval_status, adminId } = body;

    // Validate required fields
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0 || !approval_status) {
      return NextResponse.json({
        success: false,
        error: 'User IDs array and approval status are required'
      }, { status: 400 });
    }

    // Validate approval status
    if (!['pending', 'approved', 'rejected'].includes(approval_status)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid approval status. Must be pending, approved, or rejected'
      }, { status: 400 });
    }

    // TODO: Add admin authentication check here

    // Batch update users
    const { data, error } = await supabaseServerClient
      .from('profiles')
      .update({
        approval_status,
        updated_at: new Date().toISOString()
      })
      .in('id', userIds)
      .select('id, email, name, role, approval_status, updated_at');

    if (error) {
      throw error;
    }

    // Log the batch approval action
    console.log(`Batch update: ${userIds.length} users approval status changed to ${approval_status} by admin ${adminId || 'unknown'}`);

    return NextResponse.json({
      success: true,
      data,
      message: `${data?.length || 0} users updated to ${approval_status} status`,
      updated_count: data?.length || 0
    }, { status: 200 });

  } catch (error: any) {
    console.error('Error batch updating user approval status:', error.message);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}