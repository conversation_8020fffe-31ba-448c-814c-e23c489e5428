import { NextRequest, NextResponse } from 'next/server';
import { supabase as supabaseServerClient } from '@/lib/supabaseClient';

// GET - Fetch approval statistics
export async function GET(req: NextRequest) {
  try {
    // Get counts by approval status
    const { data: statusCounts, error: statusError } = await supabaseServerClient
      .from('profiles')
      .select('approval_status')
      .not('approval_status', 'is', null);

    if (statusError) {
      throw statusError;
    }

    // Count by status
    const stats = {
      pending: 0,
      approved: 0,
      rejected: 0,
      total: 0
    };

    statusCounts?.forEach(profile => {
      if (profile.approval_status === 'pending') stats.pending++;
      else if (profile.approval_status === 'approved') stats.approved++;
      else if (profile.approval_status === 'rejected') stats.rejected++;
      stats.total++;
    });

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: recentUsers, error: recentError } = await supabaseServerClient
      .from('profiles')
      .select('approval_status, created_at')
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: false });

    if (recentError) {
      throw recentError;
    }

    // Calculate recent stats
    const recentStats = {
      new_registrations: recentUsers?.length || 0,
      pending_this_month: recentUsers?.filter(u => u.approval_status === 'pending').length || 0,
      approved_this_month: recentUsers?.filter(u => u.approval_status === 'approved').length || 0,
      rejected_this_month: recentUsers?.filter(u => u.approval_status === 'rejected').length || 0
    };

    // Get approval rate
    const approvalRate = stats.total > 0 ?
      Math.round((stats.approved / (stats.approved + stats.rejected)) * 100) : 0;

    return NextResponse.json({
      success: true,
      data: {
        overview: stats,
        recent: recentStats,
        approval_rate: approvalRate,
        last_updated: new Date().toISOString()
      }
    }, { status: 200 });

  } catch (error: any) {
    console.error('Error fetching approval statistics:', error.message);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}