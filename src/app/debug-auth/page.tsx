"use client";

import { useAuth } from "@/context/AuthContext";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";

const DebugAuthPage = () => {
  const { user, loading, isApproved, isPending } = useAuth();
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [profileInfo, setProfileInfo] = useState<any>(null);

  useEffect(() => {
    const getSessionInfo = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setSessionInfo(session);
      
      if (session?.user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();
        setProfileInfo(profile);
      }
    };
    
    getSessionInfo();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Auth Debug Information</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Auth Context</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({
              user,
              loading,
              isApproved,
              isPending
            }, null, 2)}
          </pre>
        </div>
        
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Session Info</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(sessionInfo, null, 2)}
          </pre>
        </div>
        
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Profile Info</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(profileInfo, null, 2)}
          </pre>
        </div>
        
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Current Location</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({
              pathname: window.location.pathname,
              href: window.location.href,
              origin: window.location.origin
            }, null, 2)}
          </pre>
        </div>

        <div className="bg-red-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Middleware Test</h2>
          <p className="text-sm mb-2">
            If you can see this page and you have pending approval status,
            then the middleware is NOT working correctly.
          </p>
          <p className="text-sm">
            Check the browser console for middleware logs starting with "🔒 MIDDLEWARE RUNNING"
          </p>
        </div>
      </div>
      
      <div className="mt-6 bg-yellow-100 p-4 rounded">
        <h2 className="text-lg font-semibold mb-3">Expected Behavior</h2>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>If user is null: Should redirect to /login</li>
          <li>If approval_status is 'pending': Should only allow /login and /pending-approval</li>
          <li>If approval_status is 'approved': Should allow all routes based on role</li>
          <li>If approval_status is 'rejected': Should sign out and redirect to /login</li>
        </ul>
      </div>
    </div>
  );
};

export default DebugAuthPage;
